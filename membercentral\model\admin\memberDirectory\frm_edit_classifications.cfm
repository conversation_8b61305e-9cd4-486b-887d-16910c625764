<cfsavecontent variable="local.classJS">
	<cfoutput>
	<script language="JavaScript">
		let classificationTable;

		function getMemberDirectoryClassificationsData_memberDirectoryGroupSets(onCompleteFunc) {
			var ajaxParams = { memberDirectoryID: #arguments.event.getValue('mdID')# };
			TS_AJX('ADMMEMBERDIRECTORY','getAvailableAndSelectedMemberDirectoryGroupSetsJSON',ajaxParams,onCompleteFunc,onCompleteFunc,60000,onCompleteFunc);
		}

		function reloadMemberDirectoryGroupSets() {
			loadGroupSetGrids_memberDirectoryGroupSets();
		}

		function deleteMemberDirectoryClassification(classificationID, onCompleteFunc) {
			var objParams = {
				memberDirectoryID: #arguments.event.getValue('mdID')#,
				memberDirectorySRID: #local.siteResourceID#,
				classificationID: classificationID
			};
			TS_AJX('ADMMEMBERDIRECTORY','removeClassification',objParams,onCompleteFunc,onCompleteFunc,10000,onCompleteFunc);
		}

		function moveMemberDirectoryClassification(classificationID, direction, onCompleteFunc) {
			var objParams = {
				memberDirectoryID: #arguments.event.getValue('mdID')#,
				classificationID: classificationID,
				dir: direction
			};
			TS_AJX('ADMMEMBERDIRECTORY','doMoveClassification',objParams,onCompleteFunc,onCompleteFunc,10000,onCompleteFunc);
		}
		function editClassification(cid) {
			MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: (cid !== 0) ? 'Edit Classification' : 'Add Classification',
			iframe: true,
			contenturl: '#this.link.editClassification#&mdid=#arguments.event.getValue('mdID')#&cid=' + cid,
			strmodalfooter: {
				classlist: 'd-flex',
				showclose: false,
				showextrabutton: true,
				extrabuttonclass: 'btn-primary ml-auto',
				extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##classificationDetails :submit").click',
				extrabuttonlabel: 'Save Details'
			}
		});
		}
		function removeClassification(cid,rowID) {
			var removeResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					$('tr.child-of-'+rowID).remove();
					$('##'+rowID).remove();
					resetClassificationRows();
				}
				else {
					delBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
					alert('We were unable to delete this classification. Try again.');
				}
			};

			let delBtn = $('##btnDelClassification'+cid);

			mca_initConfirmButton(delBtn, function(){
				var objParams = { memberDirectoryID:#arguments.event.getValue('mdID')#, memberDirectorySRID:#local.siteResourceID#, classificationID:cid };
				TS_AJX('ADMMEMBERDIRECTORY','removeClassification',objParams,removeResult,removeResult,10000,removeResult);
			});
		}
		function moveClassification(cid,rowID,dir) {
			var moveResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					moveClassificationsGridRow(rowID,dir);
				}
			};
			var objParams = { memberDirectoryID:#arguments.event.getValue('mdID')#, classificationID:cid, dir:dir };
			TS_AJX('ADMMEMBERDIRECTORY','doMoveClassification',objParams,moveResult,moveResult,10000,moveResult);
		}
		function moveClassificationsGridRow(rowID,dir) {
			let movingRow, targetRow;

			if(dir == 'up'){
				movingRow = $('##'+rowID);
				targetRow = movingRow.closest('tr').prevAll('tr.child-of-gridRoot:first');
			}
			else {
				movingRow = $('##'+rowID).closest('tr').nextAll('tr.child-of-gridRoot:first'); /*next row will be moved to top*/
				targetRow = $('##'+rowID);
			}

			let movingRowID = movingRow.attr('id');
			movingRow.addClass('moveRow-' + movingRowID);
			$('tr.child-of-'+movingRowID).addClass('moveRow-' + movingRowID);

			let arrMoveRows = $('tr.moveRow-'+movingRowID);
			arrMoveRows.remove().insertBefore(targetRow);
			$('tr.moveRow-'+movingRowID).removeClass('moveRow-' + movingRowID);
			resetClassificationRows();
		}
		function resetClassificationRows() {
			let childRows = $('table##classificationTable tr.child-of-gridRoot');
			if (childRows.length > 1) {
				childRows.find('a.gridRowMoveUp,a.gridRowMoveDown').removeClass('invisible');
				childRows.find('a.gridRowMoveUp').first().addClass('invisible');
				childRows.find('a.gridRowMoveDown').last().addClass('invisible');
			} else {
				childRows.find('a.gridRowMoveUp,a.gridRowMoveDown').addClass('invisible');
			}
		}
		function toggleParentRow(rowID) {
			let rowToggleBtn = $('##displayLabel_'+rowID+' i.rowToggleBtn');
			rowToggleBtn.toggleClass('fa-plus-square fa-minus-square');
			let showChildren = rowToggleBtn.hasClass('fa-minus-square');
			$('tr.child-of-'+rowID).toggleClass('d-none',!showChildren);
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.classJS#">
<cfoutput>
<div class="card mb-1">
	<div class="card-header bg-light p-1">
		<div class="card-header--title font-size-lg ml-2">Classifications</div>
	</div>
	<div class="card-body p-1">
		#local.memberDirectoryGroupSetWidget.html#
	</div>
</div>
</cfoutput>