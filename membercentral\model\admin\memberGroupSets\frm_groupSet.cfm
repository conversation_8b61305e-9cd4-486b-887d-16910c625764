<cfif arguments.event.getValue('mca_ta','') eq 'edit'>		
	<cfsavecontent variable="local.gridJS">
		<cfoutput>
		<script type="text/javascript">
			let groupTable;

			function initializeGroupTable() {
				groupTable = $('##groupTable').DataTable({
					"processing": true,
					"serverSide": true,
					"paginate": false,
					"language": {
						"lengthMenu": "_MENU_"
					},
					"ajax": { 
						"url": "#local.groupListLink#",
						"type": "post",
						"data": function(d) { 
								if (window.reorderData && window.reorderData.length > 0) { 
									d.reorderData = JSON.stringify(window.reorderData); 
									window.reorderData = [];
								} 
								return d; 
						}
					},
					"autoWidth": false,
					"columns": [
						{
							"data": null,
							"render": function (data, type) {
								let renderData = '';
								if (type === 'display') {
									renderData += '<i class="fa-light fa-bars"></i>';
									
								}
								return type === 'display' ? renderData : data;
							},
							"width": "5%",
							"orderable": false
						},
						{ "data": null,
							"render": function ( data, type, row, meta ) {
								let renderData = '';
								if (type === 'display') {
									renderData += '<div class="form-group mb-0">';
									renderData += '<div class="text-dim small mb-1">'+data.groupPathExpanded+'</div>';
									renderData += '<div class="form-label-group mb-0"><input type="text" data-groupSetGroupID="'+data.groupSetGroupID+'" id="labeloverride_'+data.groupSetGroupID+'" name="labeloverride_'+data.groupSetGroupID+'" class="form-control" onchange="doLabelOverride('+data.groupSetGroupID+')" placeholder="'+data.groupName+'" size="40" value="'+data.labelOverride+'">';
									renderData += '<label for="labeloverride_'+data.groupSetGroupID+'">Label Override</label></div>';
									renderData += '</div>';
								}
								return type === 'display' ? renderData : data;
							},
							"width": "75%",
							"orderable": false 
						},
						{ "data": null,
							"render": function ( data, type, row, meta ) {
								let renderData = '';
								if (type === 'display') {
									renderData += '<a href="##" onclick="removeGroup('+data.groupSetGroupID+');return false;" id="btnDel'+data.groupSetGroupID+'" class="btn btn-xs text-danger p-1 m-1" title="Delete Group"><i class="fa-solid fa-trash-can"></i></a>';
								}
								return type === 'display' ? renderData : data;
							},
							"width": "15%",
							"className": "text-center",
							"orderable": false
						}
					],
					"ordering": false,
					"rowReorder": {
						dataSrc: "columnid" 
					},
					"searching": false
				});
				groupTable.on('row-reorder', function (e, diff, edit) {
					let orderData = [];
					diff.forEach(function(item){
						orderData.push({
							id: groupTable.row(item.node).data().groupSetGroupID,
							newOrder: item.newPosition
						});
					});
					window.reorderData = orderData;
				});
			}
			function addGroup() {
				$('##divGSForm').addClass('d-none');
				top.$('##MCModalFooter').addClass('d-none');
				$("##divGSFormSubmitArea").addClass('d-none')
				$('##divGSFormContainer')
					.removeClass('d-none')
					.html(mca_getLoadingHTML())
					.load('#this.link.addGroup#&gsID=#arguments.event.getValue('gsID',0)#');
			}
			function doLabelOverride(gsgid) {
				var labelOverride = function(r) {
					if (r.success && r.success.toLowerCase() == 'true'){
						return true;
					} else {
						delElement.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
						if(r.msg) alert(r.msg);
						else alert('We were unable to Label Override this group. Try again.');
					}
				};

				var objParams = { gsgid:gsgid, gsid:#arguments.event.getValue('gsID',0)#, labeloverride:$('##labeloverride_'+gsgid).val()  };
				TS_AJX('MEMBERGROUPSETS','doLabelOverrideGroupInGroupSet',objParams,labelOverride,labelOverride,10000,labelOverride);
			}
			function cancelGSGroups() {
				$('##divGSFormContainer').html('').addClass('d-none');
				$('##divGSForm').removeClass('d-none');
				top.$('##MCModalFooter').removeClass('d-none');
			}
			function removeGroup(gsgid) {
				var removeItem = function(r) {
					if (r.success && r.success.toLowerCase() == 'true'){
						groupTable.draw();
						top.reloadGroupSetsTable();
					} else {
						delElement.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
						if(r.msg) alert(r.msg);
						else alert('We were unable to delete this group. Try again.');
					}
				};

				let delElement = $('##btnDel'+gsgid);
				mca_initConfirmButton(delElement, function(){
					var objParams = { gsgid:gsgid, gsid:#arguments.event.getValue('gsID',0)# };
					TS_AJX('MEMBERGROUPSETS','doRemoveGroup',objParams,removeItem,removeItem,10000,removeItem);
				});
			}
			function reloadPage() { groupTable.draw(); }

			function validateAndSaveGroupSet() {
				$('##divMsg').hide();

				$('##divGSForm').hide();
				$('##divGSFormSaveLoading').show();

				var arrReq = new Array();
				var groupSetName = $('##groupSetName').val().trim();
				

				if(groupSetName.length == 0){
					arrReq[arrReq.length] = 'Please enter a group set name.';
				}
				<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and arguments.event.getValue('gsID',0) gt 0>
					var groupSetUID = $('##gsUID').val().trim();
					if(groupSetUID.length == 0){
						arrReq[arrReq.length] = 'UID is required.';
					}
				</cfif>

				if(arrReq.length > 0){
					showErr(arrReq.join('<br/>'));
					$('##divGSForm').show();
					$('##divGSFormSaveLoading').hide();
					return false;
				}

				let validateGroupSetName = new Promise(function(resolve, reject) {
					var checkGroupSetNameResult = function(r) {
						if (r.success && r.success.toLowerCase() == 'true') {
							if (!r.gsnameinuse) {
								resolve();
							} else {
								reject({errmsg:'That name is already in use.'});
							}
						} else {
							reject({errmsg:'Unable to validate Group Set Name.'});
						}
					};

					var objParams = {
						gsID: #arguments.event.getValue('gsID',0)#,
						groupSetName: groupSetName
					};
					TS_AJX('MEMBERGROUPSETS','checkGroupSetName',objParams,checkGroupSetNameResult,checkGroupSetNameResult,10000,checkGroupSetNameResult);
				});

				let saveGroupSet = function() {
					return new Promise(function(resolve, reject) {
						var saveGroupSetResult = function(r) {
							if (r.success && r.success.toLowerCase() == 'true') {
								resolve(r);
							} else {
								reject({errmsg:'Unable to save Group Set.'});
							}
						};
						
						<cfif arguments.event.getValue('gsID',0) gt 0>
							var objParams = {
								groupSetID: #arguments.event.getValue('gsID',0)#,
								groupSetName: groupSetName,
								groupSetUID: groupSetUID
							};
							TS_AJX('MEMBERGROUPSETS','saveMemberGroupSet',objParams,saveGroupSetResult,saveGroupSetResult,10000,saveGroupSetResult);
						<cfelse>
							var objParams = {
								groupSetName: groupSetName,
								siteResourceID: $('##siteResourceID').val(),
								area: $('##area').val()
							};
							TS_AJX('MEMBERGROUPSETS','doCreateMemberGroupSet',objParams,saveGroupSetResult,saveGroupSetResult,10000,saveGroupSetResult);
						</cfif>
					});
				};

				let onSaveComplete = function(result) {
					$('##divGSFormSaveLoading').hide();

					<cfif arguments.event.getValue('gsID',0) eq 0>
						if(result.gsid) {
							var editURL = '#this.link.edit#&gsID=' + result.gsid;
							if(typeof top.loadGroupSetGrids_dashGroupSetID === 'function') {
								top.loadGroupSetGrids_dashGroupSetID(result.gsid);
							}
							if(typeof top.loadGroupSetGrids_groupsetattop === 'function') {
								top.loadGroupSetGrids_groupsetattop(result.gsid);
							}
							if(typeof top.loadGroupSetGrids_memberSettingsGroupSets === 'function') {
								top.loadGroupSetGrids_memberSettingsGroupSets();
							}
							if(typeof top.loadGroupSetGrids_memberSettingsLinkedGroupSets === 'function') {
								top.loadGroupSetGrids_memberSettingsLinkedGroupSets();
							}
							if(typeof top.loadGroupSetGrids_projectDetailsGroupSets === 'function') {
								top.loadGroupSetGrids_projectDetailsGroupSets();
							}
							if(typeof top.loadGroupSetGrids_projectProspectContactGroupSets === 'function') {
								top.loadGroupSetGrids_projectProspectContactGroupSets();
							}
							if(typeof top.loadGroupSetGrids_indyMatchSettingsGroupSets === 'function') {
								top.loadGroupSetGrids_indyMatchSettingsGroupSets();
							}
							if(typeof top.loadGroupSetGrids_referralClassificationsGroupSets === 'function') {
								top.loadGroupSetGrids_referralClassificationsGroupSets();
							}
							$('##divMsg').html('Group Set created successfully.').removeClass('alert-danger').addClass('alert-success').show().fadeOut(3000);
							top.$('##MCModalLabel').text('Edit Group Set');
							window.setTimeout(function(){ self.location.href = editURL; },2000);
						}
					<cfelse>
						$('##divGSForm').show();
						if(typeof top.reloadGroupSetsTable === 'function') {
							top.reloadGroupSetsTable();
						}
						if(typeof top.loadGroupSetGrids_dashGroupSetID === 'function') {
							top.loadGroupSetGrids_dashGroupSetID();
						}
						if(typeof top.loadGroupSetGrids_memberSettingsGroupSets === 'function') {
							top.loadGroupSetGrids_memberSettingsGroupSets();
						}
						if(typeof top.loadGroupSetGrids_memberSettingsLinkedGroupSets === 'function') {
							top.loadGroupSetGrids_memberSettingsLinkedGroupSets();
						}
						if(typeof top.loadGroupSetGrids_projectDetailsGroupSets === 'function') {
							top.loadGroupSetGrids_projectDetailsGroupSets();
						}
						if(typeof top.loadGroupSetGrids_projectProspectContactGroupSets === 'function') {
							top.loadGroupSetGrids_projectProspectContactGroupSets();
						}
						if(typeof top.loadGroupSetGrids_indyMatchSettingsGroupSets === 'function') {
							top.loadGroupSetGrids_indyMatchSettingsGroupSets();
						}
						if(typeof top.loadGroupSetGrids_referralClassificationsGroupSets === 'function') {
							top.loadGroupSetGrids_referralClassificationsGroupSets();
						}
						$('##divMsg').html('Group Set saved successfully.').removeClass('alert-danger').addClass('alert-success').show().fadeOut(3000);
					</cfif>
				};

				let onError = function(error) {
					$('##divGSFormSaveLoading').hide();
					$('##divGSForm').show();
					showErr(error.errmsg || 'An error occurred while saving the Group Set.');
				};

				validateGroupSetName
					.then(saveGroupSet)
					.then(onSaveComplete)
					.catch(onError);

				return false;
			}
			function showErr(msg){
				$('##divMsg').html(msg).removeClass('alert-success').addClass('alert-danger').show();
			}
			$(function() {
				initializeGroupTable();
			});
		</script>
		</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#local.gridJS#">
</cfif>

<cfoutput>
<div class="alert alert-danger hidden" id="divMsg"></div>
<div id="divGSForm" class="p-3">
	<form method="post" name="frm_createGroupSet" id="frm_createGroupSet"  onsubmit="return validateAndSaveGroupSet();">
		<input type="hidden" name="gsID"  id="gsID" value="#arguments.event.getValue('gsID',0)#">
		<input type="hidden" name="siteResourceID" id="siteResourceID" value="#arguments.event.getValue('siteResourceID',0)#">
		<input type="hidden" name="area" id="area" value="#arguments.event.getValue('area','')#">
		<div class="form-group">
			<div class="form-label-group">
				<input type="text" autocomplete="off" maxlength="200" name="groupSetName" id="groupSetName" value="#local.groupSet.groupSetName#" class="form-control">
				<label for="groupSetName">Group Set Name</label>
			</div>
		</div>
		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and arguments.event.getValue('gsID',0) gt 0>
			<div class="form-group">
				<div class="form-label-group">
					<input type="text" autocomplete="off" maxlength="60" name="gsUID" id="gsUID" value="#local.groupSet.uid#" class="form-control">
					<label for="gsUID">API ID</label>
				</div>
			</div>
		<cfelse>
			<cfif arguments.event.getValue('gsID',0) gt 0>
				<div class="form-group">
					<div class="form-label-group">
						<input type="text" autocomplete="off" maxlength="60" name="gsUID" id="gsUID" value="#local.groupSet.uid#" class="form-control-plaintext" readOnly="true">
						<label for="gsUID">API ID</label>
					</div>
				</div>
			</cfif>
		</cfif>
		<button type="submit" name="btnSubmit" class="btn btn-primary btn-sm d-none">Save</button>
			
	</form>

	<cfif arguments.event.getValue('mca_ta','') eq 'edit' AND arguments.event.getValue('gsID',0) gt 0>
		<div class="mt-3">
			<div class="text-right">
				<button type="button" name="btnAddGroup" class="btn btn-sm btn-secondary" onclick="addGroup();">
					<span class="btn-wrapper--icon"><i class="fa-regular fa-circle-plus"></i></span>
					<span class="btn-wrapper--label">Add Group to Group Set</span>
				</button>
			</div>
			<table id="groupTable" class="table table-sm table-striped table-bordered" style="width:100%">
				<thead>
					<tr>
						<th id="columnid"></th>
						<th>Groups</th>
						<th>Remove</th>
					</tr>
				</thead>
			</table>
		</div>
	</cfif>
</div>
<div id="divGSFormContainer" class="p-3 d-none"></div>
<div id="divGSFormSaveLoading" class="d-none">
	<div class="mt-4 text-center">
		<div class="spinner-border" role="status"></div>
		<div class="font-weight-bold mt-2">Please wait while we validate and save the details.</div>
	</div>
</div>
<div id="divGSFormSubmitArea" class="d-none"></div>
</cfoutput>